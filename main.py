from collections import Counter

#
import spacy

#
import requests

#
from bs4 import BeautifulSoup

# https://github.com/wikimedia/pywikibot
# https://doc.wikimedia.org/pywikibot/stable/
import pywikibot


nlp = spacy.load("fr_core_news_md")


def extract_text_from_url(page_url, timeout=10):
    """
    Extract text content from an HTML page at the given URL.

    Args:
        page_url (str): The URL of the HTML page to extract text from
        timeout (int): Request timeout in seconds (default: 10)

    Returns:
        str: Extracted text content from the HTML page

    Raises:
        requests.RequestException: If there's an error fetching the URL
        Exception: If there's an error parsing the HTML
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    try:
        response = requests.get(page_url, headers=headers, timeout=timeout)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, "html.parser")

        for script in soup(["script", "style"]):
            script.decompose()

        text = soup.get_text()

        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = " ".join(chunk for chunk in chunks if chunk)

        return text
    except requests.RequestException as e:
        raise requests.RequestException(f"Error fetching URL {page_url}: {str(e)}")
    except Exception as e:
        raise Exception(f"Error parsing HTML from {page_url}: {str(e)}")


def wikidata_search(query, language="fr", limit=5):
    params = {
        "action": "wbsearchentities",
        "format": "json",
        "language": language,
        "search": query,
        "limit": limit,
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    r = requests.get(
        "https://www.wikidata.org/w/api.php", params=params, headers=headers, timeout=10
    )
    results = r.json().get("search", [])
    return results


def wikidata_entity_info(entity_id):
    url = f"https://www.wikidata.org/wiki/Special:EntityData/{entity_id}.json"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    r = requests.get(url, headers=headers, timeout=10)
    data = r.json()
    entity = data["entities"][entity_id]

    sitelinks_count = len(entity.get("sitelinks", {}))
    label = (
        entity.get("labels", {})
        .get("fr", {})
        .get("value", entity.get("labels", {}).get("en", {}).get("value", ""))
    )
    description = (
        entity.get("descriptions", {})
        .get("fr", {})
        .get("value", entity.get("descriptions", {}).get("en", {}).get("value", ""))
    )

    return {
        "id": entity_id,
        "label": label,
        "description": description,
        "sitelinks": sitelinks_count,
    }


def extract_key_entities(text, top_n=5):
    doc = nlp(text)

    ents = [ent.text for ent in doc.ents]  # if ent.label_ in {"PER", "LOC", "ORG"}]

    wdsite = pywikibot.Site("wikidata", "wikidata")
    wdlanguage = "fr"
    wdparams = {
        "action": "wbsearchentities",
        "format": "json",
        "language": wdlanguage,
        "limit": 5,
        "search": "",
    }
    wdrepo = wdsite.data_repository()

    results = []

    for ent_text in ents:
        wdparams["search"] = ent_text
        wdreq = wdsite.simple_request(**wdparams)
        wdres = wdreq.submit()

        best_candidate = None
        for candidate in wdres.get("search", []):
            if candidate.get("label", "") == ent_text:
                best_candidate = candidate
                break

            aliases = candidate.get("aliases", [])
            if any(ent_text == alias for alias in aliases):
                best_candidate = candidate
                break

        if best_candidate:
            wditem = pywikibot.ItemPage(wdrepo, best_candidate["id"])
            wditem.get()

            results.append(
                {
                    "text": ent_text,
                    "id": best_candidate["id"],
                    "label": best_candidate["label"],
                    "description": best_candidate.get("description", ""),
                }
            )

    return results[:top_n]


try:
    url = "https://fr.wikipedia.org/wiki/Emmanuel_Macron"
    url = "https://zonetuto.fr/"
    print(f"Extraction du texte depuis: {url}")

    extracted_text = extract_text_from_url(url)
    url_entities = extract_key_entities(extracted_text, top_n=5)

    for ent in url_entities:
        print(f"{ent['label']} ({ent['id']})")
        print(f" - Description: {ent['description']}")
        print(f" - Popularité (sitelinks): {ent['sitelinks']}")
        print()
except (requests.RequestException, ValueError) as e:
    print(f"Erreur lors de l'extraction depuis l'URL: {e}")
    print(
        "Vous pouvez essayer avec une autre URL ou vérifier votre connexion internet."
    )
